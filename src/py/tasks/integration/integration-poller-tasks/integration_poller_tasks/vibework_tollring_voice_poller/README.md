**SGC-Voice Poller** 

`sgc_voice_poll` is a poller flow that runs on schedule, gets voice recording metadata and MP3 attachments via SGC APIs, and uploads them to object store.

## Approach

The poller follows these steps:

1. Retrieves the last execution time from workflow configuration
2. Determines the polling interval (from_date and to_date) based on the last execution
3. Makes API call to the SGC API to get recordings between the specified date range:
   - URL: https://extprov.myphones.net/recordings.aspx
   - Parameters include API key, date range, and search command
4. For each recording in the metadata:
   - Downloads the MP3 file from the recording URL
   - Saves the MP3 to the object store
   - Creates and uploads an individual metadata JSON file for each recording

## API Details

Initial call:
```
https://extprov.myphones.net/recordings.aspx?ctok={api_key}&c=search&sd={start_date}&ed={end_date}
```

The API has the following parameters:
- `ctok`: Client auth token (This is provided by the client and should be stored in vault )
- `c`: Command verb - can be 'search' or 'delete'
- `sd`: Start date in format YYYYMMDDHHMMSS (HHMMSS is optional)
- `ed`: End date in format YYYYMMDDHHMMSS (HHMMSS is optional)
- `ni`: Optional - name information (resolve numbers to names where possible) - 1=resolve

The date parameters use the format "YYYYMMDDhhmmss".

**API Rules**:
- `ed - sd` must be < 7 days
- `sd` must be past-dated
- `sd` must be older than `ed`
- API is only accessible between 12:00AM UK time to 6:00AM UK time

The date parameters use the format "YYYYMMDDhhmmss".

According to the client :- "There is no pagination or rate limiting on the API - besides the operational hours and the limit of only being able to query <7 days of records per request (as per the document). You can setup your application to rate limit the requests, by specifying smaller request windows. E.g. Requesting calls for periods of 1 hour or 1 day, instead of the full permitted 7 days.
There is no ability to limit/paginate results returned based on call count, e.g. 10 calls as you suggested, only by using the start datetime/end datetime fields previously mentioned. Although there is nothing stopping you from separating the data you receive from us into smaller chunks and actioning these piecemeal."

## Storage Paths

Sample attachment file path:
```
s3://{tenant.lake_prefix}/aries/ingress/streamed/{poller_name}/{date}/{sid}.mp3
```

Sample metadata path:
```
s3://{tenant.lake_prefix}/aries/ingress/streamed/{poller_name}/{date}/{sid}.json
```

## Sample Response from API (metadata)

```json
{
    "myphones": {
        "recordings": [
            {
                "sid": "481eeaf-9e5a22d5@***********",
                "rs": "20250404113706",
                "cg": "02073628894",
                "cd": "0041793632885",
                "t": "1299",
                "sz": "3065112",
                "ic": "0",
                "p": "https://content-h.telephony-cloud.com/content/dHR3...",
                "rpg": "1",
                "rpd": "0"
            },
            {
                "sid": "b419da78-acaff175@***********",
                "rs": "20250404122921",
                "cg": "02073628894",
                "cd": "0033144941900",
                "t": "83",
                "sz": "194112",
                "ic": "0",
                "p": "https://content-h.telephony-cloud.com/content/dHR3...",
                "rpg": "1",
                "rpd": "0"
            }
        ]
    }
}
```
### Response Attributes

| Attribute | Description |
|-----------|-------------|
| sid | Unique identifier for recording |
| cg | Calling CLI – may be anonymous/blank if the caller withheld their number |
| cd | Called CLI |
| rs | Recording started date/time |
| t | Duration of recording |
| sz | Size of the resultant recording |
| p | Full path of the https-based exposure of the binary recording |
| ic | Additional field |
| rpg | Additional field |
| rpd | Additional field |

When using `ni=1` parameter, the following additional fields may be available:
- `ng`: Name of owner of calling CLI
- `nd`: Name of owner of called CLI
- `ag`: Name of owning company of calling CLI
- `ad`: Name of owning company of called CLI

## Sample Normal Run Input

```
workflow = WorkflowFieldSet(
    name="sgc_voice_poll",
    stack="dev-blue",
    tenant="pinafore",
    start_timestamp=datetime.now(),
)
input_param = IOParamFieldSet(
    params=dict()
)
task = TaskFieldSet(name="test", version="latest", success=False)
return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
```

The poller also supports polling data into a custom path.
If `custom_lake_path` is passed, then use custom_lake_path + aries/ingress/... as the landing path.
By default, it is set to None.

Sample with custom lake path:

```
workflow = WorkflowFieldSet(
    name="sgc_voice_poll",
    stack="dev-blue",
    tenant="pinafore",
    start_timestamp=datetime.datetime.now(),
)
input_param = IOParamFieldSet(
    params=dict(
        custom_lake_path='onboarding/',  # optional
    )
)
task = TaskFieldSet(name="test", version="latest", success=False)
return AriesTaskInput(workflow=workflow, input_param=input_param, task=task)
```

App metrics are added when there is a failure event:
  - `app_metric.metrics["generic"]["errored_count"] += 1`


## Date Range and Backfill

The poller supports regular polling intervals based on the last execution time.
Backfill functionality is available through the standard poll interval and backfill mechanism.