import datetime
import json
import logging
import requests
from aries_se_comms_tasks.feeds.voice.sgc_voice.static import SGCApiResponseAttribute
from aries_task_link.models import AriesTaskInput, AriesTaskResult
from data_platform_config_api_client.tenant_workflow import TenantWorkflowAPI
from integration_poller_tasks.sgc_voice_poller.static import Static
from se_comms_ingress_utils.abstractions.api_voice_poller import AbstractAPIVoicePoller
from se_comms_ingress_utils.common_util import (
    get_poll_interval_and_backfill,
    update_poller_last_execution_time,
)
from se_data_lake.lake_path import get_streamed_poller_file_path
from se_fsspec_utils.file_utils import retriable_call
from urllib.parse import urlencode

logger = logging.getLogger(Static.POLLER_NAME)
ATTACHMENT_FAILURES = []


class SomeAttachmentsNotDownloadedException(Exception):
    pass


class MetadataDownloadFailureException(Exception):
    pass


class SGCVoicePoll(AbstractAPIVoicePoller):
    def __init__(self, aries_task_input: AriesTaskInput, config):
        super().__init__(aries_task_input, config)
        self._attachment_failures: list = []
        self._tenant_workflow_api = TenantWorkflowAPI(self._config_api_client)

    def _get_metadata(self, from_date: datetime, to_date: datetime):
        params = {
            Static.CTOK: self._secrets.api_key,
            Static.CMD: "search",
            Static.START_DATE: from_date.strftime("%Y%m%d%H%M%S"),
            Static.END_DATE: to_date.strftime("%Y%m%d%H%M%S"),
        }
        url = f"{Static.BASE_URL}?{urlencode(params)}"
        logger.info(f"[FETCHING] metadata from SGC API: {url}")
        response = self._requests_retriable_call(
            requests.get,
            url=url,
            headers={"Authorization": f"Bearer {self._secrets.api_key}"},
            timeout=Static.REQUEST_TIMEOUT,
        )
        if response.content == b"":
            logger.warning("[WARNING] Empty response content from SGC API.")
            return []
        return response.json().get("myphones", {}).get("recordings", [])

    def _get_attachment(self, recording_url: str) -> bytes:
        response = self._requests_retriable_call(
            requests.get,
            recording_url,
            stream=True,
            headers={"Authorization": f"Bearer {self._secrets.api_key}"},
            timeout=Static.REQUEST_TIMEOUT,
        )
        if response.status_code != 200:
            raise Exception(
                f"Failed to download MP3 from {recording_url}, status={response.status_code}"
            )
        return response.content

    def run_poller(self) -> AriesTaskResult:
        poll_interval_and_backfill_info = get_poll_interval_and_backfill(
            aries_task_input=self._aries_task_input,
            workflow_last_executed=self._poller_tenant_workflow_config.workflow_last_executed,
            timestamp_now=self._timestamp_now,
        )
        from_date = poll_interval_and_backfill_info.from_date
        end_date = poll_interval_and_backfill_info.to_date
        # convert datetime to isoformat
        poll_from_date = from_date.isoformat()
        poll_to_date = end_date.isoformat()

        logger.info(
            f"poll_from_date: {poll_from_date},"
            f"poll_to_date: {poll_to_date},"
            f" backfill: {poll_interval_and_backfill_info.backfill}"
        )

        streamed_path_prefix = get_streamed_poller_file_path(
            workflow_name=self._workflow_name,
            date=self._timestamp_now.date().isoformat(),
            custom_path=self._custom_lake_path,
            is_evented=True,
        )
        object_prefix = (
            f"{self._poller_tenant_workflow_config.tenant.lake_prefix.rstrip('/')}"
            f"/{streamed_path_prefix}"
        )

        try:
            metadata = self._get_metadata(
                from_date=poll_interval_and_backfill_info.from_date,
                to_date=poll_interval_and_backfill_info.to_date,
            )
        except Exception as e:
            logger.error(f"[ERROR] Failed to fetch SGC metadata: {str(e)}")
            raise MetadataDownloadFailureException("Metadata could not be fetched from the API")

        if not metadata:
            logger.info(
                f"[NO METADATA FOUND] No metadata fetched from {poll_from_date} to {poll_to_date}"
            )
            return AriesTaskResult(output_param=None, app_metric=self._app_metric)

        logger.info(f"[DOWNLOADED] {len(metadata)} metadata records from SGC API")

        for record in metadata:
            sid = record.get(SGCApiResponseAttribute.SID)
            recording_url = record.get(SGCApiResponseAttribute.PATH)
            if not sid or not recording_url:
                logger.warning(f"[SKIPPING] Missing sid or recording_url: {record}")
                continue

            attachment_path = f"{streamed_path_prefix.rstrip('/')}/{sid}.mp3"
            full_s3_uri = (
                f"{self._poller_tenant_workflow_config.tenant.lake_prefix.rstrip('/')}"
                f"/{attachment_path}"
            )

            try:
                logger.info(f"[DOWNLOADING] MP3 from {recording_url} from  SGC API")
                with retriable_call(self._destination_fs.open, full_s3_uri, mode="wb") as f:
                    f.write(self._get_attachment(recording_url))
                logger.info(f"[UPLOADED] MP3 to {full_s3_uri}")

                # Create and upload individual metadata file with matching name
                metadata_path = f"{object_prefix}/{sid}.json"
                with retriable_call(self._destination_fs.open, metadata_path, mode="w") as f:
                    json.dump(record, f)
                logger.info(f"[UPLOADED] metadata file to {metadata_path}")

            except Exception as e:
                logger.error(f"[ERROR] Failed to process recording {sid}: {str(e)}")
                logger.error(f"[ERROR] Recording URL: {recording_url}")
                self._app_metric.metrics["generic"]["errored_count"] += 1
                ATTACHMENT_FAILURES.append(sid)

        update_poller_last_execution_time(
            last_execution_time=end_date.strftime("%Y-%m-%dT%H:%M:%S"),
            tenant_name=self._tenant_name,
            workflow_name=self._workflow_name,
            flow_env_vars=self._config,
        )

        if ATTACHMENT_FAILURES:
            logger.error(f"Attachment download failures: {ATTACHMENT_FAILURES}")
            raise SomeAttachmentsNotDownloadedException(
                "Some of the attachments were not downloaded"
            )

        return AriesTaskResult(output_param=None, app_metric=self._app_metric)
